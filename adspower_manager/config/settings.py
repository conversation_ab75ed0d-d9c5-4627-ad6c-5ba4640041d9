"""
Modern configuration system using Pydantic for validation and type safety
"""
import os
import logging
from datetime import timedelta
from functools import lru_cache
from typing import Optional, Literal
from pydantic import BaseSettings, Field, validator, PostgresDsn, AnyHttpUrl
from pydantic.env_settings import SettingsSourceCallable


class DatabaseSettings(BaseSettings):
    """Database configuration"""
    url: str = Field(default="sqlite:///instance/app.db", env="DATABASE_URL")
    echo: bool = Field(default=False, env="DATABASE_ECHO")
    pool_size: int = Field(default=10, env="DATABASE_POOL_SIZE")
    max_overflow: int = Field(default=20, env="DATABASE_MAX_OVERFLOW")
    pool_timeout: int = Field(default=30, env="DATABASE_POOL_TIMEOUT")
    pool_recycle: int = Field(default=3600, env="DATABASE_POOL_RECYCLE")

    @validator('url')
    def validate_database_url(cls, v):
        if v.startswith('postgresql://'):
            # Convert postgresql:// to postgresql+psycopg2://
            v = v.replace('postgresql://', 'postgresql+psycopg2://', 1)
        return v


class JWTSettings(BaseSettings):
    """JWT configuration"""
    secret_key: str = Field(..., env="JWT_SECRET_KEY")
    access_token_expires_hours: int = Field(default=24, env="JWT_ACCESS_TOKEN_EXPIRES_HOURS")
    refresh_token_expires_days: int = Field(default=30, env="JWT_REFRESH_TOKEN_EXPIRES_DAYS")
    algorithm: str = Field(default="HS256", env="JWT_ALGORITHM")

    @property
    def access_token_expires(self) -> timedelta:
        return timedelta(hours=self.access_token_expires_hours)

    @property
    def refresh_token_expires(self) -> timedelta:
        return timedelta(days=self.refresh_token_expires_days)


class MailSettings(BaseSettings):
    """Email configuration"""
    server: str = Field(default="smtp.163.com", env="MAIL_SERVER")
    port: int = Field(default=465, env="MAIL_PORT")
    use_tls: bool = Field(default=False, env="MAIL_USE_TLS")
    use_ssl: bool = Field(default=True, env="MAIL_USE_SSL")
    username: Optional[str] = Field(default=None, env="MAIL_USERNAME")
    password: Optional[str] = Field(default=None, env="MAIL_PASSWORD")
    default_sender: Optional[str] = Field(default=None, env="MAIL_DEFAULT_SENDER")
    debug: bool = Field(default=False, env="MAIL_DEBUG")
    code_expiry_minutes: int = Field(default=10, env="EMAIL_CODE_EXPIRY_MINUTES")


class AdsPowerSettings(BaseSettings):
    """AdsPower API configuration"""
    api_base: AnyHttpUrl = Field(default="https://api-global.adspower.net/v1", env="ADSPOWER_API_BASE")
    api_key: Optional[str] = Field(default=None, env="ADSPOWER_API_KEY")
    use_protocol_mode: bool = Field(default=True, env="USE_PROTOCOL_MODE")
    enable_webdriver: bool = Field(default=False, env="ENABLE_WEBDRIVER")
    webdriver_pool_size: int = Field(default=10, env="WEBDRIVER_POOL_SIZE")
    webdriver_timeout: int = Field(default=1800, env="WEBDRIVER_DRIVER_TIMEOUT")
    webdriver_check_interval: int = Field(default=300, env="WEBDRIVER_CHECK_INTERVAL")


class SecuritySettings(BaseSettings):
    """Security configuration"""
    secret_key: str = Field(..., env="SECRET_KEY")
    use_sso: bool = Field(default=True, env="USE_SSO")
    cors_origins: list[str] = Field(default=["*"], env="CORS_ORIGINS")
    rate_limit_per_minute: int = Field(default=60, env="RATE_LIMIT_PER_MINUTE")
    session_timeout_hours: int = Field(default=24, env="SESSION_TIMEOUT_HOURS")


class LoggingSettings(BaseSettings):
    """Logging configuration"""
    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = Field(default="json", env="LOG_FORMAT")  # json or text
    correlation_id_header: str = Field(default="X-Correlation-ID", env="LOG_CORRELATION_ID_HEADER")

    @validator('level')
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'Log level must be one of: {valid_levels}')
        return v.upper()


class MonitoringSettings(BaseSettings):
    """Monitoring and metrics configuration"""
    enable_metrics: bool = Field(default=True, env="ENABLE_METRICS")
    metrics_port: int = Field(default=9090, env="METRICS_PORT")
    health_check_timeout: int = Field(default=30, env="HEALTH_CHECK_TIMEOUT")


class Settings(BaseSettings):
    """Main application settings"""
    # Application
    app_name: str = Field(default="AdsPower Manager", env="APP_NAME")
    app_version: str = Field(default="2.0.0", env="APP_VERSION")
    environment: Literal["development", "testing", "production"] = Field(default="production", env="APP_ENV")
    debug: bool = Field(default=False, env="DEBUG")
    port: int = Field(default=5000, env="PORT")
    host: str = Field(default="0.0.0.0", env="HOST")

    # Sub-configurations
    database: DatabaseSettings = DatabaseSettings()
    jwt: JWTSettings = JWTSettings()
    mail: MailSettings = MailSettings()
    adspower: AdsPowerSettings = AdsPowerSettings()
    security: SecuritySettings = SecuritySettings()
    logging: LoggingSettings = LoggingSettings()
    monitoring: MonitoringSettings = MonitoringSettings()

    # Claude Sync API
    claude_sync_api_key: str = Field(default="default-sync-key-123", env="CLAUDE_SYNC_API_KEY")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
        @classmethod
        def customise_sources(
            cls,
            init_settings: SettingsSourceCallable,
            env_settings: SettingsSourceCallable,
            file_secret_settings: SettingsSourceCallable,
        ) -> tuple[SettingsSourceCallable, ...]:
            return (
                init_settings,
                env_settings,
                file_secret_settings,
            )

    @validator('environment')
    def validate_environment(cls, v):
        if v not in ['development', 'testing', 'production']:
            raise ValueError('Environment must be development, testing, or production')
        return v

    @property
    def is_development(self) -> bool:
        return self.environment == "development"

    @property
    def is_testing(self) -> bool:
        return self.environment == "testing"

    @property
    def is_production(self) -> bool:
        return self.environment == "production"

    @property
    def sqlalchemy_database_uri(self) -> str:
        return self.database.url

    @property
    def log_level(self) -> int:
        return getattr(logging, self.logging.level)


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()
